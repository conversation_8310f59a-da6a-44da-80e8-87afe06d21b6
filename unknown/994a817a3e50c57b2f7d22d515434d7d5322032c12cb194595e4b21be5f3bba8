import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Animated, Dimensions } from 'react-native';
import { ChevronRight, Gift, Star, Zap } from 'lucide-react-native';
import { MonetizationManager } from '../utils/monetization';
import * as Haptics from 'expo-haptics';

interface MonetizationBannerProps {
  position?: 'top' | 'bottom' | 'inline';
  category?: string;
  style?: 'gradient' | 'solid' | 'minimal';
  showAnimation?: boolean;
}

export function MonetizationBanner({ 
  position = 'bottom',
  category = 'general',
  style = 'gradient',
  showAnimation = true
}: MonetizationBannerProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    // Check if we can show monetization
    if (!MonetizationManager.canShowMonetization()) {
      setIsVisible(false);
      return;
    }

    if (showAnimation) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
    }
  }, []);

  const handleBannerPress = async () => {
    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Track and open monetization
    const success = await MonetizationManager.openMonetizationUrl('banner', category);
    
    if (success) {
      // Animate out after successful click
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setIsVisible(false);
      });
    }
  };

  const handleDismiss = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  if (!isVisible) {
    return null;
  }

  const getBannerStyle = () => {
    switch (style) {
      case 'gradient':
        return 'bg-gradient-to-r from-blue-600 to-purple-600';
      case 'solid':
        return 'bg-blue-600';
      case 'minimal':
        return 'bg-gray-800 border border-gray-600';
      default:
        return 'bg-gradient-to-r from-blue-600 to-purple-600';
    }
  };

  const getIcon = () => {
    switch (category.toLowerCase()) {
      case 'movies':
        return <Star size={24} color="white" />;
      case 'tv shows':
        return <Zap size={24} color="white" />;
      case 'books':
        return <Gift size={24} color="white" />;
      default:
        return <Gift size={24} color="white" />;
    }
  };

  const getMessage = () => {
    return MonetizationManager.getContextualMessage(category);
  };

  const getPositionStyles = () => {
    switch (position) {
      case 'top':
        return 'mt-2 mb-4';
      case 'bottom':
        return 'mb-2 mt-4';
      case 'inline':
        return 'my-3';
      default:
        return 'mb-2 mt-4';
    }
  };

  return (
    <Animated.View
      style={{
        opacity: fadeAnim,
        transform: [{ translateY: slideAnim }],
      }}
      className={`mx-4 ${getPositionStyles()}`}
    >
      <TouchableOpacity 
        onPress={handleBannerPress}
        className={`${getBannerStyle()} p-4 rounded-xl shadow-lg`}
        activeOpacity={0.8}
      >
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <View className="mr-3">
              {getIcon()}
            </View>
            <View className="flex-1">
              <Text className="text-white font-bold text-base">
                🎁 Special Offers
              </Text>
              <Text className="text-blue-100 text-sm mt-1" numberOfLines={2}>
                {getMessage()}
              </Text>
            </View>
          </View>
          
          <View className="ml-3">
            <ChevronRight size={24} color="white" />
          </View>
        </View>
        
        {/* Pulse animation for attention */}
        <View className="absolute top-2 right-2">
          <View className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
        </View>
      </TouchableOpacity>
      
      {/* Dismiss option for minimal style */}
      {style === 'minimal' && (
        <TouchableOpacity 
          onPress={handleDismiss}
          className="absolute top-1 right-1 p-2"
        >
          <Text className="text-gray-400 text-xs">✕</Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
}

// Compact banner for tab navigation
export function CompactMonetizationBanner({ category = 'general' }: { category?: string }) {
  const handlePress = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    await MonetizationManager.openMonetizationUrl('tab_banner', category);
  };

  if (!MonetizationManager.canShowMonetization()) {
    return null;
  }

  return (
    <TouchableOpacity 
      onPress={handlePress}
      className="bg-gradient-to-r from-yellow-500 to-orange-500 px-3 py-2 rounded-lg mr-2"
      activeOpacity={0.8}
    >
      <Text className="text-white font-bold text-xs">💰 OFFERS</Text>
    </TouchableOpacity>
  );
}

// Floating action button style
export function FloatingMonetizationButton({ category = 'general' }: { category?: string }) {
  const [isVisible, setIsVisible] = useState(true);
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    if (!MonetizationManager.canShowMonetization()) {
      setIsVisible(false);
      return;
    }

    // Pulse animation
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    
    pulse.start();
    
    return () => pulse.stop();
  }, []);

  const handlePress = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    const success = await MonetizationManager.openMonetizationUrl('floating_button', category);
    
    if (success) {
      setIsVisible(false);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={{
        transform: [{ scale: pulseAnim }],
      }}
      className="absolute bottom-20 right-4 z-10"
    >
      <TouchableOpacity
        onPress={handlePress}
        className="bg-gradient-to-r from-green-500 to-blue-500 w-14 h-14 rounded-full justify-center items-center shadow-lg"
        activeOpacity={0.8}
      >
        <Gift size={24} color="white" />
      </TouchableOpacity>
    </Animated.View>
  );
}
