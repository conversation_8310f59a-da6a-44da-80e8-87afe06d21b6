import React, { useState, memo } from "react"; // Added memo
import { View, Text, TouchableOpacity, StyleSheet, Platform, Image } from "react-native"; // Using React Native Image
// import { Image } from "expo-image"; // Temporarily disabled expo-image
import { ExternalLink, Star, Globe, Film, Tv, BookOpen, ImageOff } from "lucide-react-native";
import Animated, { useAnimatedStyle, useSharedValue, withTiming, withSequence, FadeIn } from "react-native-reanimated";
import * as Haptics from "expo-haptics";

// Cross-platform shadow utility
const createShadowStyle = (elevation: number = 8) => {
  if (Platform.OS === 'web') {
    return {
      boxShadow: `0px 4px 5px rgba(0, 0, 0, 0.3)`,
    };
  }
  return {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation,
  };
};


interface WebsiteCardProps {
  id: string;
  name: string;
  description?: string;
  logoUrl: string;
  url?: string;
  isFavorite?: boolean;
  onPress: (site: {
    id: string;
    name: string;
    description?: string;
    logoUrl: string;
    url?: string;
    isFavorite?: boolean;
  }) => void;
  onToggleFavorite?: () => void;
  displayMode?: "horizontal" | "vertical" | "grid";
  category?: string;
}

const WebsiteCard = ({
  id = "1",
  name = "IMDb",
  description,
  logoUrl = "imdb",
  url = "https://www.imdb.com",
  isFavorite = false,
  onPress = () => {},
  onToggleFavorite,
  displayMode = "horizontal",
  category = "Movies",
}: WebsiteCardProps) => {
  // Animation values
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  // Handle the press event with the website data
  const handlePress = () => {
    // Provide haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Animate the press
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );

    onPress({ id, name, description, logoUrl, url, isFavorite });
  };


  // State for image loading and errors
  const [imageLoading, setImageLoading] = useState(true); // Assume loading initially
  const [imageLoadError, setImageLoadError] = useState(false);

  // Logo URL mapping for websites that use string identifiers
  const logoUrlMap: Record<string, string> = {
    amazonprime: "https://via.placeholder.com/64x64/FF9900/FFFFFF?text=Prime",
    barnesandnoble: "https://via.placeholder.com/64x64/003366/FFFFFF?text=B%26N",
    disneyplus: "https://via.placeholder.com/64x64/113CCF/FFFFFF?text=D%2B",
    fandango: "https://via.placeholder.com/64x64/FF6600/FFFFFF?text=F",
    goodreads: "https://via.placeholder.com/64x64/553B08/FFFFFF?text=GR",
    hbomax: "https://via.placeholder.com/64x64/8A2BE2/FFFFFF?text=HBO",
    hulu: "https://via.placeholder.com/64x64/1CE783/FFFFFF?text=Hulu",
    imdb: "https://via.placeholder.com/64x64/F5C518/000000?text=IMDb",
    kinopoisk: "https://via.placeholder.com/64x64/FF6600/FFFFFF?text=KP",
    letterboxd: "https://via.placeholder.com/64x64/00D735/FFFFFF?text=LB",
    metacritic: "https://via.placeholder.com/64x64/FFCC33/000000?text=MC",
    netflix: "https://via.placeholder.com/64x64/E50914/FFFFFF?text=Netflix",
    rottentomatoes: "https://via.placeholder.com/64x64/FA320A/FFFFFF?text=RT",
    tvguide: "https://via.placeholder.com/64x64/0066CC/FFFFFF?text=TV",
  };

  // Determine the image source based on the logoUrl
  const getImageSource = () => {
    // For testing, let's use a simple test image for all logos
    const testImageUrl = "https://via.placeholder.com/64x64/FF0000/FFFFFF?text=TEST";
    console.log(`[getImageSource for ${name}] Using test URL: ${testImageUrl}`);
    return { uri: testImageUrl };

    // Original logic commented out for testing
    /*
    if (typeof logoUrl === 'string' && logoUrl.startsWith('http')) {
      // HTTP/HTTPS URL - return as URI
      console.log(`[getImageSource for ${name}] Using HTTP URL: ${logoUrl}`);
      return { uri: logoUrl };
    }

    if (typeof logoUrl === 'string' && logoUrlMap[logoUrl]) {
      // String identifier - return mapped URL
      const mappedUrl = logoUrlMap[logoUrl];
      console.log(`[getImageSource for ${name}] Mapping "${logoUrl}" to: ${mappedUrl}`);
      return { uri: mappedUrl };
    }

    // Fallback to a working placeholder
    console.warn(`[getImageSource for ${name}] logoUrl "${logoUrl}" not found. Using placeholder.`);
    return require('../../assets/images/partial-react-logo.png'); // Use a known working asset
    */
  };

  // Get the appropriate icon based on category
  const getCategoryIcon = () => {
    switch(category) {
      case "Movies":
        return <Film size={16} color="#FF5252" style={{ marginRight: 4 }} />;
      case "TV Shows":
        return <Tv size={16} color="#2196F3" style={{ marginRight: 4 }} />;
      case "Books":
        return <BookOpen size={16} color="#4CAF50" style={{ marginRight: 4 }} />;
      default:
        return <Globe size={16} color="#9E9E9E" style={{ marginRight: 4 }} />;
    }
  };

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  // Horizontal mode (compact) - iPhone style
  if (displayMode === "horizontal") {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          className="items-center mx-3 w-16"
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={{
            ...createShadowStyle(8),
            borderRadius: 12
          }}>
            {imageLoading ? (
              <SkeletonPlaceholder width={64} height={64} style={{ borderRadius: 12 }} />
            ) : imageLoadError ? (
              <View style={[styles.errorIconContainer, { width: 64, height: 64 }]}>
                <ImageOff size={32} color="#9ca3af" /> {/* bg-gray-400 */}
              </View>
            ) : (
              <Image
                source={getImageSource()}
                style={styles.logoImageHorizontal}
                resizeMode="contain"
                onLoadStart={() => {
                  console.log(`[${name}] Image loading started for:`, JSON.stringify(getImageSource()));
                  setImageLoading(true);
                  setImageLoadError(false);
                }}
                onLoadEnd={() => {
                  console.log(`[${name}] Image loaded successfully`);
                  setImageLoading(false);
                }}
                onError={(e) => {
                  console.error(`[${name}] Image failed to load. Source:`, JSON.stringify(getImageSource()), 'Error:', e);
                  setImageLoading(false);
                  setImageLoadError(true);
                }}
              />
            )}
          </View>
          <View className="flex-row items-center mt-2 justify-center">
            <View className="mr-1">
              {getCategoryIcon()}
            </View>
            <Text
              className="text-white text-xs text-center font-bold"
              numberOfLines={1}
              style={{ fontFamily: 'System', letterSpacing: 0.5 }}
            >
              {name}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Grid mode (for 3-column layout)
  if (displayMode === "grid") {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          className="items-center p-2"
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={{
            ...createShadowStyle(8),
            borderRadius: 12
          }}>
            {imageLoading ? (
              <SkeletonPlaceholder width={64} height={64} style={{ borderRadius: 12, marginBottom: 8 }} />
            ) : imageLoadError ? (
              <View style={[styles.errorIconContainer, { width: 64, height: 64, marginBottom: 8 }]}>
                <ImageOff size={32} color="#9ca3af" />
              </View>
            ) : (
              <Image
                source={getImageSource()}
                style={styles.logoImageGrid}
                resizeMode="contain"
                onLoadStart={() => {
                  setImageLoading(true);
                  setImageLoadError(false);
                }}
                onLoadEnd={() => setImageLoading(false)}
                onError={(e) => {
                  console.error(`[Grid Image Error for ${name}] Source: ${JSON.stringify(getImageSource())}, Error:`, e);
                  setImageLoading(false);
                  setImageLoadError(true);
                }}
              />
            )}
          </View>
          <View className="w-full items-center">
            <View className="flex-row items-center justify-center">
              {getCategoryIcon()}
              <Text
                className="text-white text-xs font-bold text-center"
                numberOfLines={2}
                style={{ fontFamily: 'System', letterSpacing: 0.5 }}
              >
                {name}
              </Text>
            </View>
            {isFavorite && (
              <Animated.View
                entering={FadeIn.springify().damping(10).stiffness(100)}
              >
                <Star size={12} color="#FFC107" fill="#FFC107" className="mt-1" />
              </Animated.View>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Vertical mode (expanded with details) - iPhone style
  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        className="flex-row items-center p-4"
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={{
          ...createShadowStyle(8),
          borderRadius: 12
        }}>
          {imageLoading ? (
            <SkeletonPlaceholder width={48} height={48} style={{ borderRadius: 12 }} />
          ) : imageLoadError ? (
            <View style={[styles.errorIconContainer, { width: 48, height: 48 }]}>
              <ImageOff size={24} color="#9ca3af" />
            </View>
          ) : (
            <Image
              source={getImageSource()}
              style={styles.logoImageVertical}
              resizeMode="contain"
              onLoadStart={() => {
                setImageLoading(true);
                setImageLoadError(false);
              }}
              onLoadEnd={() => setImageLoading(false)}
              onError={(e) => {
                console.error(`[Vertical Image Error for ${name}] Source: ${JSON.stringify(getImageSource())}, Error:`, e);
                setImageLoading(false);
                setImageLoadError(true);
              }}
            />
          )}
        </View>
        <View className="flex-1 ml-3">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <View className="mr-1">
                {getCategoryIcon()}
              </View>
              <Text
                className="text-white text-base font-bold"
                style={{ fontFamily: 'System', letterSpacing: 0.5 }}
              >
                {name}
              </Text>
            </View>
            {isFavorite && (
              <Animated.View
                entering={FadeIn.springify().damping(10).stiffness(100)}
              >
                <Star size={16} color="#FFC107" fill="#FFC107" />
              </Animated.View>
            )}
          </View>
          {description && (
            <Text className="text-gray-400 text-xs mt-1" numberOfLines={1}>
              {description}
            </Text>
          )}
        </View>
        <ExternalLink size={16} color="#A0AEC0" className="ml-2" />
      </TouchableOpacity>
    </Animated.View>
  );
};

interface SkeletonPlaceholderProps {
  width: number;
  height: number;
  style?: object; // Allow additional styles
}

// Simple Skeleton Placeholder Component
const SkeletonPlaceholder: React.FC<SkeletonPlaceholderProps> = ({ width, height, style }) => (
  <View style={[
    styles.skeleton,
    { width, height },
    style
  ]} />
);

const styles = StyleSheet.create({
  logoImageHorizontal: {
    width: 64,
    height: 64,
    borderRadius: 12,
  },
  logoImageGrid: {
    width: 64,
    height: 64,
    borderRadius: 12,
    marginBottom: 8,
  },
  logoImageVertical: {
    width: 48, // Corrected from 80 to 48 to match className="w-12 h-12"
    height: 48,
    borderRadius: 12, // Corrected from 16 to 12 to match rounded-xl
  },
  skeleton: {
    backgroundColor: '#374151', // bg-gray-700
    borderRadius: 12, // Corresponds to rounded-xl
  },
  errorIconContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#374151', // bg-gray-700
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default memo(WebsiteCard);
