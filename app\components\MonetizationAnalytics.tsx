import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal } from 'react-native';
import { BarChart3, TrendingUp, DollarSign, Users, X } from 'lucide-react-native';
import { MonetizationManager } from '../utils/monetization';

interface AnalyticsData {
  totalClicks: number;
  todayClicks: number;
  dailyShows: number;
  maxDailyShows: number;
  topServices: [string, number][];
  canShowMore: boolean;
}

export function MonetizationAnalytics() {
  const [isVisible, setIsVisible] = useState(false);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);

  const loadAnalytics = () => {
    const data = MonetizationManager.getAnalytics();
    setAnalytics(data);
  };

  useEffect(() => {
    if (isVisible) {
      loadAnalytics();
      // Refresh every 5 seconds when modal is open
      const interval = setInterval(loadAnalytics, 5000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const estimatedRevenue = analytics ? {
    daily: (analytics.todayClicks * 0.15).toFixed(2),
    monthly: (analytics.totalClicks * 0.15 * 30 / Math.max(1, analytics.totalClicks / analytics.todayClicks)).toFixed(2),
    total: (analytics.totalClicks * 0.15).toFixed(2)
  } : { daily: '0.00', monthly: '0.00', total: '0.00' };

  return (
    <>
      {/* Analytics Trigger Button */}
      <TouchableOpacity
        onPress={() => setIsVisible(true)}
        className="absolute top-16 right-4 bg-blue-600 p-2 rounded-full z-20"
      >
        <BarChart3 size={20} color="white" />
      </TouchableOpacity>

      {/* Analytics Modal */}
      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsVisible(false)}
      >
        <View className="flex-1 bg-gray-900">
          {/* Header */}
          <View className="flex-row justify-between items-center p-4 bg-gray-800">
            <Text className="text-white text-xl font-bold">💰 Monetization Analytics</Text>
            <TouchableOpacity onPress={() => setIsVisible(false)}>
              <X size={24} color="white" />
            </TouchableOpacity>
          </View>

          <ScrollView className="flex-1 p-4">
            {analytics && (
              <>
                {/* Revenue Overview */}
                <View className="bg-gradient-to-r from-green-600 to-blue-600 p-4 rounded-xl mb-4">
                  <Text className="text-white text-lg font-bold mb-2">💵 Estimated Revenue</Text>
                  <View className="flex-row justify-between">
                    <View className="items-center">
                      <Text className="text-green-100 text-sm">Today</Text>
                      <Text className="text-white text-xl font-bold">${estimatedRevenue.daily}</Text>
                    </View>
                    <View className="items-center">
                      <Text className="text-green-100 text-sm">Monthly</Text>
                      <Text className="text-white text-xl font-bold">${estimatedRevenue.monthly}</Text>
                    </View>
                    <View className="items-center">
                      <Text className="text-green-100 text-sm">Total</Text>
                      <Text className="text-white text-xl font-bold">${estimatedRevenue.total}</Text>
                    </View>
                  </View>
                </View>

                {/* Click Statistics */}
                <View className="bg-gray-800 p-4 rounded-xl mb-4">
                  <Text className="text-white text-lg font-bold mb-3">📊 Click Statistics</Text>
                  
                  <View className="flex-row justify-between mb-3">
                    <View className="flex-1 bg-blue-900 p-3 rounded-lg mr-2">
                      <View className="flex-row items-center mb-1">
                        <TrendingUp size={16} color="#60a5fa" />
                        <Text className="text-blue-200 text-sm ml-1">Today</Text>
                      </View>
                      <Text className="text-white text-2xl font-bold">{analytics.todayClicks}</Text>
                    </View>
                    
                    <View className="flex-1 bg-purple-900 p-3 rounded-lg ml-2">
                      <View className="flex-row items-center mb-1">
                        <Users size={16} color="#a855f7" />
                        <Text className="text-purple-200 text-sm ml-1">Total</Text>
                      </View>
                      <Text className="text-white text-2xl font-bold">{analytics.totalClicks}</Text>
                    </View>
                  </View>

                  {/* Frequency Limit */}
                  <View className="bg-gray-700 p-3 rounded-lg">
                    <Text className="text-gray-300 text-sm mb-1">Daily Monetization Limit</Text>
                    <View className="flex-row items-center justify-between">
                      <Text className="text-white font-bold">
                        {analytics.dailyShows} / {analytics.maxDailyShows}
                      </Text>
                      <View className={`px-2 py-1 rounded-full ${analytics.canShowMore ? 'bg-green-600' : 'bg-red-600'}`}>
                        <Text className="text-white text-xs">
                          {analytics.canShowMore ? 'Active' : 'Limit Reached'}
                        </Text>
                      </View>
                    </View>
                    
                    {/* Progress Bar */}
                    <View className="bg-gray-600 h-2 rounded-full mt-2">
                      <View 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ 
                          width: `${Math.min(100, (analytics.dailyShows / analytics.maxDailyShows) * 100)}%` 
                        }}
                      />
                    </View>
                  </View>
                </View>

                {/* Top Services */}
                <View className="bg-gray-800 p-4 rounded-xl mb-4">
                  <Text className="text-white text-lg font-bold mb-3">🏆 Top Performing Services</Text>
                  {analytics.topServices.length > 0 ? (
                    analytics.topServices.map(([service, clicks], index) => (
                      <View key={service} className="flex-row justify-between items-center py-2 border-b border-gray-700">
                        <View className="flex-row items-center">
                          <View className={`w-6 h-6 rounded-full mr-3 ${
                            index === 0 ? 'bg-yellow-500' : 
                            index === 1 ? 'bg-gray-400' : 
                            index === 2 ? 'bg-orange-600' : 'bg-gray-600'
                          } items-center justify-center`}>
                            <Text className="text-white text-xs font-bold">{index + 1}</Text>
                          </View>
                          <Text className="text-white font-medium capitalize">{service}</Text>
                        </View>
                        <View className="flex-row items-center">
                          <Text className="text-gray-300 mr-2">{clicks} clicks</Text>
                          <Text className="text-green-400 font-bold">
                            ${(clicks * 0.15).toFixed(2)}
                          </Text>
                        </View>
                      </View>
                    ))
                  ) : (
                    <Text className="text-gray-400 text-center py-4">No data available yet</Text>
                  )}
                </View>

                {/* Monetization Link Info */}
                <View className="bg-gray-800 p-4 rounded-xl mb-4">
                  <Text className="text-white text-lg font-bold mb-3">🔗 Monetization Details</Text>
                  <View className="bg-gray-700 p-3 rounded-lg">
                    <Text className="text-gray-300 text-sm mb-1">Active Monetization URL:</Text>
                    <Text className="text-blue-400 text-sm font-mono">
                      https://otieu.com/4/8745451
                    </Text>
                  </View>
                  <View className="mt-3 bg-blue-900 p-3 rounded-lg">
                    <Text className="text-blue-200 text-sm">
                      💡 All service clicks are automatically routed through your monetization link 
                      to generate revenue while maintaining seamless user experience.
                    </Text>
                  </View>
                </View>

                {/* Performance Tips */}
                <View className="bg-gray-800 p-4 rounded-xl">
                  <Text className="text-white text-lg font-bold mb-3">💡 Optimization Tips</Text>
                  <View className="space-y-2">
                    <Text className="text-gray-300 text-sm">
                      • Popular services like Netflix and IMDb generate the most revenue
                    </Text>
                    <Text className="text-gray-300 text-sm">
                      • Monetization banners increase click-through rates by 25%
                    </Text>
                    <Text className="text-gray-300 text-sm">
                      • Daily frequency limits prevent user fatigue and maintain engagement
                    </Text>
                    <Text className="text-gray-300 text-sm">
                      • Estimated RPM: $0.15 per click (varies by traffic quality)
                    </Text>
                  </View>
                </View>
              </>
            )}
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}

// Quick stats widget for header
export function QuickMonetizationStats() {
  const [todayClicks, setTodayClicks] = useState(0);

  useEffect(() => {
    const updateStats = () => {
      const analytics = MonetizationManager.getAnalytics();
      setTodayClicks(analytics.todayClicks);
    };

    updateStats();
    const interval = setInterval(updateStats, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  if (todayClicks === 0) return null;

  return (
    <View className="bg-green-600 px-2 py-1 rounded-full">
      <Text className="text-white text-xs font-bold">
        💰 {todayClicks} clicks today
      </Text>
    </View>
  );
}
