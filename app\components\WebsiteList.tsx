import React, { memo } from "react"; // Added memo
import { View, ScrollView } from "react-native";
import CategorySection from "./CategorySection";

const MemoizedCategorySection = memo(CategorySection); // Memoize CategorySection

interface Website {
  id: string;
  name: string;
  description: string;
  url: string;
  logo: string;
  category: string;
  isFavorite: boolean;
}

interface WebsiteListProps {
  websites?: Website[];
  onWebsitePress?: (website: Website) => void;
  onToggleFavorite?: (websiteId: string) => void;
  showFavoritesOnly?: boolean;
}

const WebsiteList = memo(({
  websites = [
    {
      id: "1",
      name: "IMDb",
      description:
        "The world's most popular and authoritative source for movie, TV, and celebrity content.",
      url: "https://www.imdb.com",
      logo: "imdb", // Using local asset
      category: "Movies",
      isFavorite: true,
    },
    {
      id: "2",
      name: "Rotten Tomatoes",
      description: "Movie and TV reviews from critics and audiences.",
      url: "https://www.rottentomatoes.com",
      logo: "rottentomatoes", // Using local asset
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "3",
      name: "Letterboxd",
      description: "Social platform for sharing your taste in film.",
      url: "https://letterboxd.com",
      logo: "letterboxd", // Using local asset
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "4",
      name: "Metacritic",
      description: "Reviews, news, and more for movies, games, and music.",
      url: "https://www.metacritic.com",
      logo: "metacritic", // Using local asset
      category: "Movies",
      isFavorite: true,
    },
    {
      id: "5",
      name: "Fandango",
      description: "Movie tickets, gift cards, and more.",
      url: "https://www.fandango.com",
      logo: "fandango",
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "6",
      name: "Kinopoisk",
      description: "Russian film database and community.",
      url: "https://www.kinopoisk.ru",
      logo: "kinopoisk",
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "7",
      name: "Netflix",
      description: "Streaming service for TV shows and movies.",
      url: "https://www.netflix.com",
      logo: "netflix",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "8",
      name: "TV Guide",
      description:
        "Find out what to watch with TV listings, streaming guides, and more.",
      url: "https://www.tvguide.com",
      logo: "tvguide",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "9",
      name: "Hulu",
      description: "Streaming service with TV shows, movies, and originals.",
      url: "https://www.hulu.com",
      logo: "hulu",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "10",
      name: "Amazon Prime",
      description: "Streaming service with movies, TV shows, and originals.",
      url: "https://www.amazon.com/Prime-Video",
      logo: "amazonprime",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "11",
      name: "Disney+",
      description:
        "Streaming service with Disney, Pixar, Marvel, Star Wars, and more.",
      url: "https://www.disneyplus.com",
      logo: "disneyplus",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "12",
      name: "HBO Max",
      description: "Streaming service with HBO, Warner Bros, and more.",
      url: "https://www.hbomax.com",
      logo: "hbomax",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "13",
      name: "Goodreads",
      description: "Book reviews, recommendations, and discussion.",
      url: "https://www.goodreads.com",
      logo: "goodreads",
      category: "Books",
      isFavorite: true,
    },
    {
      id: "14",
      name: "Barnes & Noble",
      description: "Books, textbooks, and more.",
      url: "https://www.barnesandnoble.com",
      logo: "barnesandnoble",
      category: "Books",
      isFavorite: false,
    },
    {
      id: "15",
      name: "Amazon Books",
      description: "Online bookstore with millions of titles.",
      url: "https://www.amazon.com/books",
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: true,
    },
    {
      id: "16",
      name: "Book Depository",
      description: "Online bookstore with free delivery worldwide.",
      url: "https://www.bookdepository.com",
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: false,
    },
    {
      id: "17",
      name: "Audible",
      description: "Audiobooks, podcasts, and more.",
      url: "https://www.audible.com",
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: true,
    },
    {
      id: "18",
      name: "LibraryThing",
      description: "Catalog your books online.",
      url: "https://www.librarything.com",
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: false,
    },
  ],
  onWebsitePress = () => {},
  onToggleFavorite = () => {},
  showFavoritesOnly = false,
}: WebsiteListProps) => {
  // Filter websites if showing favorites only
  const filteredWebsites = showFavoritesOnly
    ? websites.filter((website) => website.isFavorite)
    : websites;

  // Group websites by category
  const websitesByCategory = filteredWebsites.reduce(
    (acc, website) => {
      if (!acc[website.category]) {
        acc[website.category] = [];
      }
      acc[website.category].push(website);
      return acc;
    },
    {} as Record<string, Website[]>,
  );

  // Sort categories to ensure consistent order
  const categories = Object.keys(websitesByCategory).sort((a, b) => {
    const order = ["Movies", "TV Shows", "Books"];
    return order.indexOf(a) - order.indexOf(b);
  });

  return (
    <ScrollView
      className="flex-1 bg-[#0c1220]"
      showsVerticalScrollIndicator={false}
    >
      {categories.map((category) => (
        <MemoizedCategorySection // Use memoized version
          key={category}
          title={category}
          websites={websitesByCategory[category]}
          onWebsitePress={onWebsitePress}
          onToggleFavorite={onToggleFavorite}
        />
      ))}
      <View className="h-6" />
    </ScrollView>
  );
});

export default WebsiteList; // Already wrapped with memo at the top
