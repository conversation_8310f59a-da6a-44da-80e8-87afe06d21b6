import React, { memo } from "react"; // Added memo
import { View, ScrollView } from "react-native";
import CategorySection from "./CategorySection";
import { MonetizationBanner } from "./MonetizationBanner";
import { MonetizationManager } from "../utils/monetization";

const MemoizedCategorySection = memo(CategorySection); // Memoize CategorySection

interface Website {
  id: string;
  name: string;
  description: string;
  url: string;
  logo: string;
  category: string;
  isFavorite: boolean;
}

interface WebsiteListProps {
  websites?: Website[];
  onWebsitePress?: (website: Website) => void;
  onToggleFavorite?: (websiteId: string) => void;
  showFavoritesOnly?: boolean;
}

const WebsiteList = memo(({
  websites = [
    {
      id: "1",
      name: "IMDb",
      description:
        "The world's most popular and authoritative source for movie, TV, and celebrity content.",
      url: MonetizationManager.wrapServiceUrl("https://www.imdb.com", "IMDb", "Movies"),
      logo: "data:image/jpeg;base64,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", // Using local asset
      category: "Movies",
      isFavorite: true,
    },
    {
      id: "2",
      name: "Rotten Tomatoes",
      description: "Movie and TV reviews from critics and audiences.",
      url: MonetizationManager.wrapServiceUrl("https://www.rottentomatoes.com", "Rotten Tomatoes", "Movies"),
      logo: "data:image/png;base64,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", // Using local asset
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "3",
      name: "Letterboxd",
      description: "Social platform for sharing your taste in film.",
      url: MonetizationManager.wrapServiceUrl("https://letterboxd.com", "Letterboxd", "Movies"),
      logo: "data:image/png;base64,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", // Using local asset
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "4",
      name: "Metacritic",
      description: "Reviews, news, and more for movies, games, and music.",
      url: MonetizationManager.wrapServiceUrl("https://www.metacritic.com", "Metacritic", "Movies"),
      logo: "metacritic", // Using local asset
      category: "Movies",
      isFavorite: true,
    },
    {
      id: "5",
      name: "Fandango",
      description: "Movie tickets, gift cards, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.fandango.com", "Fandango", "Movies"),
      logo: "fandango",
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "6",
      name: "Kinopoisk",
      description: "Russian film database and community.",
      url: MonetizationManager.wrapServiceUrl("https://www.kinopoisk.ru", "Kinopoisk", "Movies"),
      logo: "kinopoisk",
      category: "Movies",
      isFavorite: false,
    },
    {
      id: "7",
      name: "Netflix",
      description: "Streaming service for TV shows and movies.",
      url: MonetizationManager.wrapServiceUrl("https://www.netflix.com", "Netflix", "TV Shows"),
      logo: "netflix",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "8",
      name: "TV Guide",
      description:
        "Find out what to watch with TV listings, streaming guides, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.tvguide.com", "TV Guide", "TV Shows"),
      logo: "tvguide",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "9",
      name: "Hulu",
      description: "Streaming service with TV shows, movies, and originals.",
      url: MonetizationManager.wrapServiceUrl("https://www.hulu.com", "Hulu", "TV Shows"),
      logo: "hulu",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "10",
      name: "Amazon Prime",
      description: "Streaming service with movies, TV shows, and originals.",
      url: MonetizationManager.wrapServiceUrl("https://www.amazon.com/Prime-Video", "Amazon Prime", "TV Shows"),
      logo: "amazonprime",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "11",
      name: "Disney+",
      description:
        "Streaming service with Disney, Pixar, Marvel, Star Wars, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.disneyplus.com", "Disney+", "TV Shows"),
      logo: "disneyplus",
      category: "TV Shows",
      isFavorite: false,
    },
    {
      id: "12",
      name: "HBO Max",
      description: "Streaming service with HBO, Warner Bros, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.hbomax.com", "HBO Max", "TV Shows"),
      logo: "hbomax",
      category: "TV Shows",
      isFavorite: true,
    },
    {
      id: "13",
      name: "Goodreads",
      description: "Book reviews, recommendations, and discussion.",
      url: MonetizationManager.wrapServiceUrl("https://www.goodreads.com", "Goodreads", "Books"),
      logo: "goodreads",
      category: "Books",
      isFavorite: true,
    },
    {
      id: "14",
      name: "Barnes & Noble",
      description: "Books, textbooks, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.barnesandnoble.com", "Barnes & Noble", "Books"),
      logo: "barnesandnoble",
      category: "Books",
      isFavorite: false,
    },
    {
      id: "15",
      name: "Amazon Books",
      description: "Online bookstore with millions of titles.",
      url: MonetizationManager.wrapServiceUrl("https://www.amazon.com/books", "Amazon Books", "Books"),
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: true,
    },
    {
      id: "16",
      name: "Book Depository",
      description: "Online bookstore with free delivery worldwide.",
      url: MonetizationManager.wrapServiceUrl("https://www.bookdepository.com", "Book Depository", "Books"),
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: false,
    },
    {
      id: "17",
      name: "Audible",
      description: "Audiobooks, podcasts, and more.",
      url: MonetizationManager.wrapServiceUrl("https://www.audible.com", "Audible", "Books"),
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: true,
    },
    {
      id: "18",
      name: "LibraryThing",
      description: "Catalog your books online.",
      url: MonetizationManager.wrapServiceUrl("https://www.librarything.com", "LibraryThing", "Books"),
      logo: "unknown", // Will use fallback placeholder
      category: "Books",
      isFavorite: false,
    },
  ],
  onWebsitePress = () => {},
  onToggleFavorite = () => {},
  showFavoritesOnly = false,
}: WebsiteListProps) => {
  // Filter websites if showing favorites only
  const filteredWebsites = showFavoritesOnly
    ? websites.filter((website) => website.isFavorite)
    : websites;

  // Group websites by category
  const websitesByCategory = filteredWebsites.reduce(
    (acc, website) => {
      if (!acc[website.category]) {
        acc[website.category] = [];
      }
      acc[website.category].push(website);
      return acc;
    },
    {} as Record<string, Website[]>,
  );

  // Sort categories to ensure consistent order
  const categories = Object.keys(websitesByCategory).sort((a, b) => {
    const order = ["Movies", "TV Shows", "Books"];
    return order.indexOf(a) - order.indexOf(b);
  });

  return (
    <ScrollView
      className="flex-1 bg-[#0c1220]"
      showsVerticalScrollIndicator={false}
    >
      {/* Top monetization banner */}
      <MonetizationBanner position="top" category="general" style="gradient" />

      {categories.map((category, index) => (
        <React.Fragment key={category}>
          <MemoizedCategorySection // Use memoized version
            title={category}
            websites={websitesByCategory[category]}
            onWebsitePress={onWebsitePress}
            onToggleFavorite={onToggleFavorite}
          />
          {/* Inline banner between categories */}
          {index === 1 && (
            <MonetizationBanner position="inline" category={category} style="minimal" />
          )}
        </React.Fragment>
      ))}

      {/* Bottom monetization banner */}
      <MonetizationBanner position="bottom" category="general" style="solid" />
      <View className="h-6" />
    </ScrollView>
  );
});

export default WebsiteList; // Already wrapped with memo at the top
