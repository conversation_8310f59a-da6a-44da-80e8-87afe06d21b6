import AsyncStorage from '@react-native-async-storage/async-storage';
import { Linking, Platform } from 'react-native';

// Web-compatible storage fallback
const storage = {
  async getItem(key: string): Promise<string | null> {
    if (Platform.OS === 'web') {
      try {
        return localStorage.getItem(key);
      } catch {
        return null;
      }
    }
    return AsyncStorage.getItem(key);
  },

  async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === 'web') {
      try {
        localStorage.setItem(key, value);
        return;
      } catch {
        return;
      }
    }
    return AsyncStorage.setItem(key, value);
  }
};

export interface MonetizationClick {
  service: string;
  timestamp: number;
  source: string;
  category: string;
}

export class MonetizationManager {
  private static readonly MONETIZATION_URL = "https://otieu.com/4/8745451";
  private static readonly MAX_DAILY_SHOWS = 15;
  private static clicks: MonetizationClick[] = [];
  private static dailyShows = 0;
  private static lastResetDate = new Date().toDateString();

  /**
   * Wrap a service URL with monetization
   */
  static wrapServiceUrl(serviceUrl: string, serviceName: string, category: string = 'general'): string {
    const params = new URLSearchParams({
      redirect: serviceUrl,
      source: 'mediahub',
      service: serviceName.toLowerCase(),
      category: category.toLowerCase(),
      timestamp: Date.now().toString()
    });
    
    return `${this.MONETIZATION_URL}?${params.toString()}`;
  }

  /**
   * Direct monetization link access
   */
  static getDirectMonetizationUrl(source: string = 'banner', category: string = 'general'): string {
    const params = new URLSearchParams({
      source: `mediahub_${source}`,
      category: category.toLowerCase(),
      timestamp: Date.now().toString()
    });
    
    return `${this.MONETIZATION_URL}?${params.toString()}`;
  }

  /**
   * Track monetization clicks
   */
  static async trackClick(serviceName: string, source: string = 'card', category: string = 'general') {
    const click: MonetizationClick = {
      service: serviceName,
      timestamp: Date.now(),
      source,
      category
    };

    this.clicks.push(click);
    this.dailyShows++;

    // Store locally for analytics
    try {
      await storage.setItem('monetization_clicks', JSON.stringify(this.clicks));
      await storage.setItem('daily_shows', this.dailyShows.toString());
      await storage.setItem('last_reset_date', this.lastResetDate);
    } catch (error) {
      console.warn('Failed to store monetization data:', error);
    }

    console.log(`💰 Monetization Click: ${serviceName} (${source}) - ${category}`);
  }

  /**
   * Check if we can show monetization (frequency capping)
   */
  static canShowMonetization(): boolean {
    const today = new Date().toDateString();
    
    if (today !== this.lastResetDate) {
      this.dailyShows = 0;
      this.lastResetDate = today;
    }
    
    return this.dailyShows < this.MAX_DAILY_SHOWS;
  }

  /**
   * Open monetization URL
   */
  static async openMonetizationUrl(source: string = 'direct', category: string = 'general') {
    if (!this.canShowMonetization()) {
      console.log('Monetization frequency limit reached for today');
      return false;
    }

    const url = this.getDirectMonetizationUrl(source, category);
    
    try {
      await Linking.openURL(url);
      await this.trackClick('direct_access', source, category);
      return true;
    } catch (error) {
      console.error('Failed to open monetization URL:', error);
      return false;
    }
  }

  /**
   * Get monetization analytics
   */
  static getAnalytics() {
    const today = new Date().setHours(0, 0, 0, 0);
    const todayClicks = this.clicks.filter(click => click.timestamp >= today);
    
    const serviceCounts = this.clicks.reduce((acc, click) => {
      acc[click.service] = (acc[click.service] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topServices = Object.entries(serviceCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    return {
      totalClicks: this.clicks.length,
      todayClicks: todayClicks.length,
      dailyShows: this.dailyShows,
      maxDailyShows: this.MAX_DAILY_SHOWS,
      topServices,
      canShowMore: this.canShowMonetization()
    };
  }

  /**
   * Load stored analytics data
   */
  static async loadStoredData() {
    try {
      const storedClicks = await storage.getItem('monetization_clicks');
      const storedDailyShows = await storage.getItem('daily_shows');
      const storedResetDate = await storage.getItem('last_reset_date');

      if (storedClicks) {
        this.clicks = JSON.parse(storedClicks);
      }

      if (storedDailyShows) {
        this.dailyShows = parseInt(storedDailyShows, 10);
      }

      if (storedResetDate) {
        this.lastResetDate = storedResetDate;
      }

      // Reset if new day
      const today = new Date().toDateString();
      if (today !== this.lastResetDate) {
        this.dailyShows = 0;
        this.lastResetDate = today;
        await storage.setItem('daily_shows', '0');
        await storage.setItem('last_reset_date', today);
      }
    } catch (error) {
      console.warn('Failed to load monetization data:', error);
    }
  }

  /**
   * Get contextual monetization message
   */
  static getContextualMessage(category: string): string {
    const messages = {
      'movies': '🎬 Discover exclusive movie deals and streaming offers!',
      'tv shows': '📺 Get premium TV streaming at discounted rates!',
      'books': '📚 Find amazing book deals and audiobook subscriptions!',
      'general': '🎁 Special offers and deals available!'
    };
    
    return messages[category.toLowerCase()] || messages.general;
  }
}

// Initialize on app start
MonetizationManager.loadStoredData();
