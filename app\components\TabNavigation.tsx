import React, { useEffect } from "react"; // Added React import
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, Image, Keyboard, Platform } from "react-native";
import { Plus, X } from "lucide-react-native";
import { BlurView } from "expo-blur";
import Animated, { useAnimatedStyle, withTiming, useSharedValue, withSpring, interpolate, Easing } from "react-native-reanimated"; // Removed FadeInDown as it's not used directly here
import * as Haptics from "expo-haptics";
import { CompactMonetizationBanner } from "./MonetizationBanner";
// Removed useTheme as it's not used after removing BottomTabNavigator
// Removed unused screen imports and Home, Settings, Search, Bookmark, Download icons from lucide-react-native

// Cross-platform shadow utility
const createShadowStyle = (opacity: number = 0.3, radius: number = 3) => {
  if (Platform.OS === 'web') {
    return {
      boxShadow: `0px 2px ${radius}px rgba(0, 0, 0, ${opacity})`,
    };
  }
  return {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: opacity,
    shadowRadius: radius,
  };
};

export interface Tab {
  id: string;
  title: string;
  url: string;
  favicon?: string;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTabId: string | null;
  onTabPress: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onNewTab: () => void;
}

export default function TabNavigation({
  tabs = [],
  activeTabId,
  onTabPress,
  onTabClose,
  onNewTab,
}: TabNavigationProps) {
  // Animated value for the tab bar visibility
  const tabBarVisible = useSharedValue(1);

  // Hide tab bar when keyboard is shown (optional, for better UX)
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        tabBarVisible.value = withTiming(0, { duration: 200 });
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        tabBarVisible.value = withTiming(1, { duration: 200 });
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: tabBarVisible.value,
      transform: [{ translateY: interpolate(tabBarVisible.value, [0, 1], [100, 0]) }],
    };
  });

  // If there are no tabs, don't render the tab bar
  if (tabs.length === 0 && !activeTabId) {
    return null;
  }

  return (
    <Animated.View
      style={[styles.tabBarContainer, animatedStyle]}
      // entering={FadeInDown.duration(300).easing(Easing.out(Easing.ease))} // Ensure FadeInDown is correctly used here if needed
    >
      <BlurView intensity={90} tint="dark" style={StyleSheet.absoluteFill}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}
        >
          {/* Compact Monetization Banner */}
          <CompactMonetizationBanner category="general" />

          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                onTabPress(tab.id);
              }}
              style={[
                styles.tabItem,
                activeTabId === tab.id && styles.activeTabItem,
              ]}
            >
              {tab.favicon ? (
                <Image
                  source={{ uri: tab.favicon }}
                  style={styles.favicon}
                  onError={() => console.log(`Error loading favicon for ${tab.title}`)}
                />
              ) : (
                <View style={styles.faviconPlaceholder} />
              )}

              <TouchableOpacity
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                  onTabClose(tab.id);
                }}
                style={styles.closeButton}
              >
                <X size={14} color={activeTabId === tab.id ? "#fff" : "#9ca3af"} />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
          {/* Add New Tab Button */}
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onNewTab();
            }}
            style={styles.newTabButton}
          >
            <Plus size={20} color="#9ca3af" />
          </TouchableOpacity>
        </ScrollView>
      </BlurView>
    </Animated.View>
  );
}

interface TabItemProps {
  tab: Tab;
  isActive: boolean;
  onPress: () => void;
  onClose: () => void;
}

function TabItem({ tab, isActive, onPress, onClose }: TabItemProps) {
  // Animation values
  const scale = useSharedValue(1);
  const opacity = useSharedValue(isActive ? 1 : 0.8);

  // Update animation values when active state changes
  useEffect(() => {
    opacity.value = withTiming(isActive ? 1 : 0.8, { duration: 200 });
    scale.value = withSpring(isActive ? 1.05 : 1, { damping: 15, stiffness: 150 });
  }, [isActive]);

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ scale: scale.value }],
    };
  });

  return (
    <Animated.View style={animatedStyle} className="mr-2">
      <TouchableOpacity
        onPress={onPress}
        className={`flex-row items-center px-3 py-2 rounded-full ${isActive ? "bg-gray-600" : "bg-gray-900"}`}
        activeOpacity={0.7}
        style={{
          ...createShadowStyle(isActive ? 0.3 : 0.1, 3),
        }}
      >
        {tab.favicon ? (
          <Image
            source={{ uri: tab.favicon }}
            className="w-5 h-5 rounded-full mr-2"
            contentFit="cover"
            transition={300}
          />
        ) : (
          <View className="w-5 h-5 rounded-full bg-gray-500 mr-2" />
        )}

        <Text
          className="text-white text-xs mr-2 max-w-24"
          numberOfLines={1}
        >
          {tab.title}
        </Text>

        <TouchableOpacity
          onPress={onClose}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          className="bg-gray-700 rounded-full p-1"
        >
          <X size={12} color="#fff" />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Define styles
const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 10,
    height: 60,
    paddingBottom: Platform.OS === 'ios' ? 20 : 10,
  },
  scrollViewContent: {
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2937',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
  },
  activeTabItem: {
    backgroundColor: '#374151',
  },
  favicon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 6,
  },
  faviconPlaceholder: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#6b7280',
    marginRight: 6,
  },
  tabTitle: {
    color: '#9ca3af',
    fontSize: 12,
    maxWidth: 100,
    marginRight: 6,
  },
  activeTabTitle: {
    color: '#ffffff',
  },
  closeButton: {
    padding: 2,
  },
  newTabButton: {
    backgroundColor: '#1f2937',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
